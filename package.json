{"name": "redline", "version": "1.0.0", "description": "A full-stack JavaScript application that automatically tracks and visualizes political violence incidents reported in Bangladeshi news media.", "main": "index.js", "scripts": {"setup": "npm install && npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "db:setup": "cd backend && npm run db:setup", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "scrape": "cd backend && npm run scrape", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint"}, "keywords": ["political-violence", "news-scraping", "ai-classification", "mapping", "bangladesh", "data-visualization"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend", "shared"]}