# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/redline_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=redline_db
DB_USER=username
DB_PASSWORD=password

# API Configuration
PORT=3001
NODE_ENV=development
API_BASE_URL=http://localhost:3001

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_MAPBOX_TOKEN=your_mapbox_token_here

# Scraping Configuration
SCRAPING_DELAY=2000
MAX_CONCURRENT_SCRAPERS=3
USER_AGENT=Mozilla/5.0 (compatible; RedlineBot/1.0)

# Scheduling
SCRAPING_SCHEDULE=0 6 * * *  # Daily at 6 AM
CLEANUP_SCHEDULE=0 2 * * 0   # Weekly at 2 AM on Sunday

# Security
JWT_SECRET=your_jwt_secret_here
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Redis (for caching and job queues)
REDIS_URL=redis://localhost:6379

# Email (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
ADMIN_EMAIL=<EMAIL>