# Bangladesh News Tracker - Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=bangladesh_news_tracker
DB_USER=your_username
DB_PASSWORD=your_password
DATABASE_URL=postgresql://your_username:your_password@localhost:5432/bangladesh_news_tracker

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.3

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Scraping Configuration
SCRAPING_DELAY_MS=2000
MAX_CONCURRENT_REQUESTS=3
USER_AGENT=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Geocoding API (Optional - for enhanced location detection)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
MAPBOX_ACCESS_TOKEN=your_mapbox_token_here

# Monitoring and Logging
SENTRY_DSN=your_sentry_dsn_here
LOG_FILE_PATH=./logs/app.log

# Security
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000

# Feature Flags
ENABLE_AI_PROCESSING=true
ENABLE_REAL_TIME_SCRAPING=true
ENABLE_LOCATION_GEOCODING=true
ENABLE_SENTIMENT_ANALYSIS=true
